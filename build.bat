@echo off
echo ========================================
echo Greek Terminal Executable Builder
echo ========================================
echo.

:menu
echo Choose build type:
echo 1. Admin Version (Never expires)
echo 2. 7-Day Trial
echo 3. 30-Day Trial  
echo 4. 30-Day Subscription
echo 5. Custom Build
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto admin
if "%choice%"=="2" goto trial7
if "%choice%"=="3" goto trial30
if "%choice%"=="4" goto subscription
if "%choice%"=="5" goto custom
if "%choice%"=="6" goto exit
echo Invalid choice. Please try again.
goto menu

:admin
echo Building Admin Version...
python build_executable.py admin --output-name "GreekTerminal-Admin"
goto done

:trial7
echo Building 7-Day Trial...
python build_executable.py trial --trial-days 7 --output-name "GreekTerminal-Trial-7Day"
goto done

:trial30
echo Building 30-Day Trial...
python build_executable.py trial --trial-days 30 --output-name "GreekTerminal-Trial-30Day"
goto done

:subscription
echo Building 30-Day Subscription...
python build_executable.py subscription --trial-days 30 --output-name "GreekTerminal-Subscription"
goto done

:custom
echo.
set /p days="Enter trial days (0 for no trial): "
set /p name="Enter output name (without .exe): "
set /p desc="Enter description: "
echo Building Custom Version...
python build_executable.py custom --trial-days %days% --output-name "%name%" --description "%desc%"
goto done

:done
echo.
echo Build completed! Check the 'dist' folder for your executable.
pause
goto menu

:exit
echo Goodbye!
pause
