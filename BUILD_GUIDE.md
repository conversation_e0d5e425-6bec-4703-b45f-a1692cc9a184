# Greek Terminal - Executable Build Guide

This guide explains how to compile your own executables for Greek Terminal.

## 🎯 Quick Start

### Option 1: Use the Build Script (Easiest)
```bash
# Windows
build.bat

# Or directly with Python
python build_executable.py admin
python build_executable.py trial --trial-days 7
python build_executable.py subscription --trial-days 30
```

### Option 2: Manual PyInstaller
```bash
pyinstaller dashboard.spec
```

## 📋 Prerequisites

1. **Python 3.8+** installed
2. **PyInstaller** installed: `pip install pyinstaller`
3. **All dependencies** installed: `pip install -r requirements.txt`

## 🔧 Build Types

### 1. Admin Version
- **Never expires**
- **Full admin access**
- **No trial limitations**

```bash
python build_executable.py admin --output-name "GreekTerminal-Admin"
```

### 2. Trial Version
- **Limited time access**
- **Configurable trial period**
- **Auto-expires after trial**

```bash
# 7-day trial
python build_executable.py trial --trial-days 7 --output-name "GreekTerminal-Trial-7Day"

# 30-day trial
python build_executable.py trial --trial-days 30 --output-name "GreekTerminal-Trial-30Day"
```

### 3. Subscription Version
- **Paid license model**
- **Configurable subscription period**
- **License key required**

```bash
python build_executable.py subscription --trial-days 30 --output-name "GreekTerminal-Subscription"
```

### 4. Custom Build
- **Fully customizable**
- **Mix and match features**

```bash
python build_executable.py custom \
  --trial-days 14 \
  --admin-mode \
  --output-name "GreekTerminal-Custom" \
  --description "14-day admin trial"
```

## 🛠️ Advanced Configuration

### Modify build_info.json Manually

Edit `build_info.json` before building:

```json
{
  "version": "trial",
  "admin_mode": false,
  "trial_mode": true,
  "build_type": "trial",
  "trial_days": 7,
  "never_expires": false,
  "description": "7-day trial version"
}
```

### Customize the Spec File

Edit `dashboard.spec` to modify:
- **Executable name**: Change `name='GreekTerminal'`
- **Icon**: Add `icon='path/to/icon.ico'`
- **Console mode**: Change `console=True/False`
- **Hidden imports**: Add to `hiddenimports` list

## 📁 Build Output

After building, you'll find:
- **Executable**: `dist/GreekTerminal.exe` (or custom name)
- **Build logs**: `build/` directory
- **Size**: Typically 150-200 MB

## 🔍 Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Add missing modules to `hiddenimports` in `dashboard.spec`
   - Install missing dependencies: `pip install module_name`

2. **Large executable size**
   - Normal for Python apps with many dependencies
   - Use `--exclude-module` to remove unused modules

3. **Slow startup**
   - First run extracts files (normal)
   - Subsequent runs are faster

4. **Missing files**
   - Add data files to `datas` in `dashboard.spec`
   - Check file paths are correct

### Debug Build Issues

```bash
# Verbose output
pyinstaller --log-level DEBUG dashboard.spec

# Check what's included
pyinstaller --analyze dashboard.spec
```

## 🎨 Customization Options

### Change Executable Icon
1. Get a `.ico` file
2. Edit `dashboard.spec`:
   ```python
   exe = EXE(
       # ... other parameters ...
       icon='path/to/your/icon.ico',
   )
   ```

### Hide Console Window
Edit `dashboard.spec`:
```python
exe = EXE(
    # ... other parameters ...
    console=False,  # Change to False
)
```

### Add Version Information
```python
# In dashboard.spec
version_info = (
    'version_info.txt',  # Create this file
    'version_info.txt'
)
```

## 📦 Distribution

### Single File Distribution
Your executable is self-contained and includes:
- Python interpreter
- All dependencies
- Your application code
- Static files and templates

### System Requirements
- **Windows 10/11** (64-bit)
- **No Python installation required**
- **~200MB disk space**

## 🔐 Security Considerations

### For Trial Builds
- Trial data stored in temp directory
- System fingerprinting prevents copying
- Automatic cleanup on expiration

### For License Builds
- Requires valid license key
- GitHub-based license validation
- Encrypted license storage

## 🚀 Automation

### Batch Build Multiple Versions
```bash
# Build all common versions
python build_executable.py admin --output-name "GT-Admin"
python build_executable.py trial --trial-days 7 --output-name "GT-Trial-7Day"
python build_executable.py trial --trial-days 30 --output-name "GT-Trial-30Day"
python build_executable.py subscription --trial-days 30 --output-name "GT-Subscription"
```

### CI/CD Integration
Add to your build pipeline:
```yaml
- name: Build Executables
  run: |
    pip install pyinstaller
    python build_executable.py trial --trial-days 7
    python build_executable.py subscription --trial-days 30
```

## 📊 Build Statistics

Typical build times and sizes:
- **Build time**: 2-5 minutes
- **Executable size**: 150-200 MB
- **Startup time**: 3-10 seconds (first run)
- **Memory usage**: 100-300 MB

## 🆘 Support

If you encounter issues:
1. Check this guide first
2. Verify all dependencies are installed
3. Try a clean build: delete `build/` and `dist/` folders
4. Check PyInstaller documentation
5. Test with a simple Python script first

## 📝 Notes

- Builds are platform-specific (Windows .exe won't run on Mac/Linux)
- Antivirus software may flag executables (false positive)
- First run may be slower due to file extraction
- Keep source code secure - executables can be reverse-engineered
