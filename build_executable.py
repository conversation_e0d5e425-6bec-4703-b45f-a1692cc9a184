#!/usr/bin/env python3
"""
Greek Terminal Executable Builder
Automates the process of building different types of executables
"""

import os
import sys
import json
import shutil
import subprocess
import argparse
from datetime import datetime

class ExecutableBuilder:
    def __init__(self):
        self.project_dir = os.path.dirname(os.path.abspath(__file__))
        self.dist_dir = os.path.join(self.project_dir, "dist")
        self.build_dir = os.path.join(self.project_dir, "build")
        
    def clean_build_dirs(self):
        """Clean previous build directories"""
        print("🧹 Cleaning previous builds...")
        for dir_path in [self.dist_dir, self.build_dir]:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                print(f"   Removed {dir_path}")
    
    def create_build_info(self, build_type, **kwargs):
        """Create build_info.json for the specified build type"""
        build_configs = {
            'admin': {
                "version": "admin",
                "admin_mode": True,
                "trial_mode": False,
                "build_type": "admin",
                "never_expires": True,
                "description": "Admin version - never expires"
            },
            'trial': {
                "version": "trial",
                "admin_mode": False,
                "trial_mode": True,
                "build_type": "trial",
                "trial_days": kwargs.get('trial_days', 7),
                "never_expires": False,
                "description": f"{kwargs.get('trial_days', 7)}-day trial version"
            },
            'subscription': {
                "version": "subscription",
                "admin_mode": False,
                "trial_mode": True,
                "build_type": "subscription",
                "trial_days": kwargs.get('trial_days', 30),
                "never_expires": False,
                "description": f"{kwargs.get('trial_days', 30)}-day subscription version"
            },
            'custom': {
                "version": kwargs.get('version', 'custom'),
                "admin_mode": kwargs.get('admin_mode', False),
                "trial_mode": kwargs.get('trial_mode', False),
                "build_type": kwargs.get('build_type', 'custom'),
                "trial_days": kwargs.get('trial_days', 0),
                "never_expires": kwargs.get('never_expires', False),
                "description": kwargs.get('description', 'Custom build')
            }
        }
        
        if build_type not in build_configs:
            raise ValueError(f"Unknown build type: {build_type}")
        
        config = build_configs[build_type]
        
        # Add build timestamp
        config['build_timestamp'] = datetime.now().isoformat()
        config['build_machine'] = os.environ.get('COMPUTERNAME', 'unknown')
        
        build_info_path = os.path.join(self.project_dir, 'build_info.json')
        with open(build_info_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"📋 Created build_info.json for {build_type} build")
        return config
    
    def build_executable(self, output_name=None):
        """Build the executable using PyInstaller"""
        print("🔨 Building executable with PyInstaller...")
        
        spec_file = os.path.join(self.project_dir, 'dashboard.spec')
        if not os.path.exists(spec_file):
            raise FileNotFoundError("dashboard.spec not found")
        
        # Run PyInstaller
        cmd = ['pyinstaller', spec_file]
        result = subprocess.run(cmd, cwd=self.project_dir, capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
        
        print("✅ Build completed successfully!")
        
        # Rename executable if custom name provided
        if output_name:
            original_exe = os.path.join(self.dist_dir, 'GreekTerminal.exe')
            new_exe = os.path.join(self.dist_dir, f'{output_name}.exe')
            if os.path.exists(original_exe):
                os.rename(original_exe, new_exe)
                print(f"📁 Renamed executable to {output_name}.exe")
        
        return True
    
    def build(self, build_type, clean=True, output_name=None, **kwargs):
        """Complete build process"""
        print(f"🚀 Starting {build_type} build...")
        
        try:
            # Clean previous builds
            if clean:
                self.clean_build_dirs()
            
            # Create build configuration
            config = self.create_build_info(build_type, **kwargs)
            
            # Build executable
            if not self.build_executable(output_name):
                return False
            
            # Show results
            exe_name = f"{output_name}.exe" if output_name else "GreekTerminal.exe"
            exe_path = os.path.join(self.dist_dir, exe_name)
            
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"🎉 Build successful!")
                print(f"   📁 Location: {exe_path}")
                print(f"   📏 Size: {file_size:.1f} MB")
                print(f"   🏷️  Type: {config['description']}")
                return True
            else:
                print("❌ Executable not found after build")
                return False
                
        except Exception as e:
            print(f"❌ Build failed: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Build Greek Terminal executables')
    parser.add_argument('build_type', choices=['admin', 'trial', 'subscription', 'custom'],
                       help='Type of build to create')
    parser.add_argument('--trial-days', type=int, default=7,
                       help='Number of trial days (for trial/subscription builds)')
    parser.add_argument('--output-name', type=str,
                       help='Custom name for the executable (without .exe)')
    parser.add_argument('--no-clean', action='store_true',
                       help='Skip cleaning previous builds')
    parser.add_argument('--admin-mode', action='store_true',
                       help='Enable admin mode (for custom builds)')
    parser.add_argument('--never-expires', action='store_true',
                       help='Never expires (for custom builds)')
    parser.add_argument('--description', type=str,
                       help='Build description (for custom builds)')
    
    args = parser.parse_args()
    
    builder = ExecutableBuilder()
    
    # Prepare kwargs for custom builds
    kwargs = {
        'trial_days': args.trial_days,
        'admin_mode': args.admin_mode,
        'never_expires': args.never_expires,
        'description': args.description
    }
    
    success = builder.build(
        build_type=args.build_type,
        clean=not args.no_clean,
        output_name=args.output_name,
        **kwargs
    )
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
